// Database schema for dbdiagram.io
// Use this at: https://dbdiagram.io/d

Table demo_requests {
  id integer [primary key, increment]
  first_name varchar(120) [not null]
  last_name varchar(120) [not null]
  email varchar(190) [not null]
  phone varchar(30)
  company_name varchar(150) [not null]
  job_title varchar(100)
  company_size varchar(50) [note: '1-10 | 11-50 | 51-200 | 201-1000 | 1000+']
  industry varchar(100)
  project_type varchar(100) [note: 'web_app | mobile_app | api | custom_software | other']
  budget_range varchar(50) [note: 'under_10k | 10k_25k | 25k_50k | 50k_100k | 100k_plus']
  timeline varchar(50) [note: 'asap | 1_month | 3_months | 6_months | 1_year_plus']
  project_description text [not null]
  specific_requirements text
  preferred_demo_date date
  preferred_demo_time varchar(20) [note: 'morning | afternoon | evening']
  how_did_you_hear varchar(100) [note: 'google | social_media | referral | event | other']
  created_at timestamptz [default: `now()`]
  status varchar(20) [default: 'new', note: 'new | contacted | demo_scheduled | demo_completed | proposal_sent | closed_won | closed_lost']
  assigned_to varchar(100)
  demo_scheduled_at timestamptz
  notes text

  indexes {
    (email, created_at) [name: 'idx_demo_email_created_at']
    status [name: 'idx_demo_status']
    preferred_demo_date [name: 'idx_demo_date']
    budget_range [name: 'idx_demo_budget']
    project_type [name: 'idx_demo_project_type']
  }
}

Table team_members {
  id integer [primary key, increment]
  full_name varchar(120) [not null]
  position varchar(100) [not null]
  bio text
  profile_image varchar(500)
  email varchar(190)
  linkedin_url varchar(300)
  github_url varchar(300)
  is_active boolean [default: true]
  display_order integer [default: 0]
  created_at timestamptz [default: `now()`]
  updated_at timestamptz [default: `now()`]
  
  indexes {
    display_order [name: 'idx_display_order']
    is_active [name: 'idx_is_active']
  }
}

Table about_us {
  id integer [primary key, increment]
  title varchar(200) [not null]
  description text [not null]
  main_image varchar(500)
  secondary_image varchar(500)
  company_name varchar(100) [not null]
  established_year integer
  mission_statement text
  vision_statement text
  values text
  is_active boolean [default: true]
  display_order integer [default: 0]
  created_at timestamptz [default: `now()`]
  updated_at timestamptz [default: `now()`]

  indexes {
    is_active [name: 'idx_about_active']
    display_order [name: 'idx_about_order']
  }
}

Table services {
  id integer [primary key, increment]
  title varchar(150) [not null]
  short_description varchar(300)
  full_description text
  icon varchar(100)
  image varchar(500)
  features text [note: 'JSON array of service features']
  price_range varchar(50)
  duration varchar(50)
  is_featured boolean [default: false]
  is_active boolean [default: true]
  display_order integer [default: 0]
  created_at timestamptz [default: `now()`]
  updated_at timestamptz [default: `now()`]

  indexes {
    is_active [name: 'idx_services_active']
    is_featured [name: 'idx_services_featured']
    display_order [name: 'idx_services_order']
  }
}

Table additional_services {
  id integer [primary key, increment]
  title varchar(150) [not null]
  subtitle varchar(200)
  description text
  icon varchar(100)
  image varchar(500)
  features text [note: 'JSON array of additional service features']
  category varchar(50) [note: 'backend | security | ui_ux | other']
  is_active boolean [default: true]
  display_order integer [default: 0]
  created_at timestamptz [default: `now()`]
  updated_at timestamptz [default: `now()`]

  indexes {
    category [name: 'idx_additional_services_category']
    is_active [name: 'idx_additional_services_active']
    display_order [name: 'idx_additional_services_order']
  }
}

Table technology_categories {
  id integer [primary key, increment]
  name varchar(50) [not null, unique]
  display_name varchar(100) [not null]
  description text
  is_active boolean [default: true]
  display_order integer [default: 0]
  created_at timestamptz [default: `now()`]

  indexes {
    name [name: 'idx_tech_categories_name']
    is_active [name: 'idx_tech_categories_active']
    display_order [name: 'idx_tech_categories_order']
  }
}

Table technologies {
  id integer [primary key, increment]
  name varchar(100) [not null]
  logo_url varchar(500)
  category_id integer [ref: > technology_categories.id]
  description text
  proficiency_level varchar(20) [default: 'intermediate', note: 'beginner | intermediate | advanced | expert']
  years_experience integer
  is_featured boolean [default: false]
  is_active boolean [default: true]
  display_order integer [default: 0]
  created_at timestamptz [default: `now()`]
  updated_at timestamptz [default: `now()`]

  indexes {
    category_id [name: 'idx_technologies_category']
    is_featured [name: 'idx_technologies_featured']
    is_active [name: 'idx_technologies_active']
    display_order [name: 'idx_technologies_order']
  }
}

Table featured_resources {
  id integer [primary key, increment]
  title varchar(200) [not null]
  description text
  image_url varchar(500) [not null]
  resource_type varchar(50) [note: 'tutorial | guide | case_study | whitepaper | video | blog_post']
  content_url varchar(500) [not null, note: 'Link to the full resource content']
  external_link boolean [default: false, note: 'true if links to external site']
  reading_time integer [note: 'Estimated reading time in minutes']
  difficulty_level varchar(20) [note: 'beginner | intermediate | advanced']
  tags text [note: 'JSON array of tags for categorization']
  author varchar(100)
  published_date date
  is_featured boolean [default: true]
  is_active boolean [default: true]
  display_order integer [default: 0]
  view_count integer [default: 0]
  created_at timestamptz [default: `now()`]
  updated_at timestamptz [default: `now()`]

  indexes {
    resource_type [name: 'idx_resources_type']
    is_featured [name: 'idx_resources_featured']
    is_active [name: 'idx_resources_active']
    display_order [name: 'idx_resources_order']
    published_date [name: 'idx_resources_published']
  }
}

Table social_media_links {
  id integer [primary key, increment]
  platform varchar(50) [not null, unique, note: 'linkedin | facebook | instagram | twitter | youtube | github | dribbble']
  display_name varchar(100) [not null]
  url varchar(500) [not null]
  icon_class varchar(100) [note: 'CSS class for icon display']
  icon_svg text [note: 'SVG icon code if custom icons used']
  is_active boolean [default: true]
  display_order integer [default: 0]
  follower_count integer [note: 'Optional: for displaying follower counts']
  created_at timestamptz [default: `now()`]
  updated_at timestamptz [default: `now()`]

  indexes {
    platform [name: 'idx_social_platform']
    is_active [name: 'idx_social_active']
    display_order [name: 'idx_social_order']
  }
}

// Notes for future reference
Note: '''
# Database Design Notes

## demo_requests
- Comprehensive "Book a Demo" form submissions
- Includes business qualification fields (budget, timeline, company size)
- Project details and requirements capture
- Lead management with status tracking and assignment
- Demo scheduling capabilities with preferred dates/times
- Source tracking for marketing attribution

## team_members  
- Optional table for dynamic team management
- Can start with static data in code and migrate later
- display_order allows custom sorting of team members
- is_active allows hiding members without deletion

## about_us
- Stores company information and about us content
- Supports multiple versions with display_order for A/B testing
- Images for visual content in about section

## services
- Main services offered by the company
- Features stored as JSON for flexibility
- Price range and duration for service planning
- Featured flag for highlighting key services

## additional_services
- Secondary/specialized services (Backend, Security, UI/UX, etc.)
- Categorized for better organization
- Separate from main services for different display treatment

## technology_categories & technologies
- Two-table structure for organizing technologies
- Categories: Frontend, Backend, Mobile, Database, etc.
- Technologies: Individual tools/languages with proficiency levels
- Supports logo URLs for visual technology showcase
- Years of experience tracking for credibility

## featured_resources
- Content marketing resources (tutorials, guides, case studies)
- Supports both internal and external content links
- Metadata for content categorization and difficulty levels
- View tracking for analytics
- Flexible tagging system for content organization

## social_media_links
- Centralized management of social media presence
- Supports custom icons and follower count display
- Platform-specific configuration with display ordering
- Easy activation/deactivation of social links

## Future Tables (not implemented yet)
- blog_posts (if blog functionality added)
- projects/portfolio (if project showcase needed)
- client_testimonials (for social proof)
- case_studies (detailed project showcases)
'''
