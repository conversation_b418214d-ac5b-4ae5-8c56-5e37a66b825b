from django.test import TestCase
from django.core.exceptions import ValidationError
from .models import AboutUs, Service, AdditionalService, FeaturedResource, SocialMediaLink


class AboutUsModelTest(TestCase):
    """Test cases for the AboutUs model."""

    def setUp(self):
        """Set up test data."""
        self.valid_about_data = {
            'title': 'About Bean Software',
            'description': 'We are a leading software development company.',
            'company_name': 'Bean Software',
            'established_year': 2020,
            'mission_statement': 'To deliver exceptional software solutions.',
            'vision_statement': 'To be the leading software company.',
            'values': 'Innovation, Quality, Customer Focus',
        }

    def test_create_about_us_with_valid_data(self):
        """Test creating an AboutUs instance with valid data."""
        about = AboutUs.objects.create(**self.valid_about_data)
        self.assertEqual(about.title, 'About Bean Software')
        self.assertEqual(about.company_name, 'Bean Software')
        self.assertTrue(about.is_active)  # Default value
        self.assertEqual(about.display_order, 0)  # Default value

    def test_about_us_str_representation(self):
        """Test the string representation of AboutUs."""
        about = AboutUs.objects.create(**self.valid_about_data)
        expected_str = "About Bean Software - Bean Software"
        self.assertEqual(str(about), expected_str)


class ServiceModelTest(TestCase):
    """Test cases for the Service model."""

    def setUp(self):
        """Set up test data."""
        self.valid_service_data = {
            'title': 'Web Development',
            'short_description': 'Custom web applications',
            'full_description': 'We build custom web applications using modern technologies.',
            'features': ['Responsive Design', 'SEO Optimized', 'Fast Loading'],
            'price_range': '$10,000 - $50,000',
            'duration': '3-6 months',
        }

    def test_create_service_with_valid_data(self):
        """Test creating a Service instance with valid data."""
        service = Service.objects.create(**self.valid_service_data)
        self.assertEqual(service.title, 'Web Development')
        self.assertFalse(service.is_featured)  # Default value
        self.assertTrue(service.is_active)  # Default value

    def test_service_str_representation(self):
        """Test the string representation of Service."""
        service = Service.objects.create(**self.valid_service_data)
        self.assertEqual(str(service), 'Web Development')

    def test_features_list_property(self):
        """Test the features_list property."""
        service = Service.objects.create(**self.valid_service_data)
        expected_features = ['Responsive Design', 'SEO Optimized', 'Fast Loading']
        self.assertEqual(service.features_list, expected_features)

        # Test with None features
        service.features = None
        self.assertEqual(service.features_list, [])


class AdditionalServiceModelTest(TestCase):
    """Test cases for the AdditionalService model."""

    def setUp(self):
        """Set up test data."""
        self.valid_additional_service_data = {
            'title': 'API Development',
            'subtitle': 'RESTful APIs',
            'description': 'We develop robust and scalable APIs.',
            'category': 'backend',
            'features': ['REST API', 'GraphQL', 'Authentication'],
        }

    def test_create_additional_service_with_valid_data(self):
        """Test creating an AdditionalService instance with valid data."""
        service = AdditionalService.objects.create(**self.valid_additional_service_data)
        self.assertEqual(service.title, 'API Development')
        self.assertEqual(service.category, 'backend')
        self.assertTrue(service.is_active)  # Default value

    def test_additional_service_str_representation(self):
        """Test the string representation of AdditionalService."""
        service = AdditionalService.objects.create(**self.valid_additional_service_data)
        expected_str = "API Development (Backend Development)"
        self.assertEqual(str(service), expected_str)


class FeaturedResourceModelTest(TestCase):
    """Test cases for the FeaturedResource model."""

    def setUp(self):
        """Set up test data."""
        self.valid_resource_data = {
            'title': 'Django Best Practices',
            'description': 'A comprehensive guide to Django development.',
            'image_url': 'https://example.com/image.jpg',
            'resource_type': 'tutorial',
            'content_url': 'https://example.com/tutorial',
            'reading_time': 15,
            'difficulty_level': 'intermediate',
            'tags': ['django', 'python', 'web development'],
            'author': 'John Developer',
        }

    def test_create_featured_resource_with_valid_data(self):
        """Test creating a FeaturedResource instance with valid data."""
        resource = FeaturedResource.objects.create(**self.valid_resource_data)
        self.assertEqual(resource.title, 'Django Best Practices')
        self.assertEqual(resource.resource_type, 'tutorial')
        self.assertTrue(resource.is_featured)  # Default value
        self.assertEqual(resource.view_count, 0)  # Default value

    def test_featured_resource_str_representation(self):
        """Test the string representation of FeaturedResource."""
        resource = FeaturedResource.objects.create(**self.valid_resource_data)
        expected_str = "Django Best Practices (Tutorial)"
        self.assertEqual(str(resource), expected_str)

    def test_tags_list_property(self):
        """Test the tags_list property."""
        resource = FeaturedResource.objects.create(**self.valid_resource_data)
        expected_tags = ['django', 'python', 'web development']
        self.assertEqual(resource.tags_list, expected_tags)

    def test_increment_view_count_method(self):
        """Test the increment_view_count method."""
        resource = FeaturedResource.objects.create(**self.valid_resource_data)
        initial_count = resource.view_count

        resource.increment_view_count()
        resource.refresh_from_db()

        self.assertEqual(resource.view_count, initial_count + 1)


class SocialMediaLinkModelTest(TestCase):
    """Test cases for the SocialMediaLink model."""

    def setUp(self):
        """Set up test data."""
        self.valid_social_data = {
            'platform': 'linkedin',
            'display_name': 'Bean Software LinkedIn',
            'url': 'https://linkedin.com/company/beansoftware',
            'follower_count': 1500,
        }

    def test_create_social_media_link_with_valid_data(self):
        """Test creating a SocialMediaLink instance with valid data."""
        social = SocialMediaLink.objects.create(**self.valid_social_data)
        self.assertEqual(social.platform, 'linkedin')
        self.assertEqual(social.display_name, 'Bean Software LinkedIn')
        self.assertTrue(social.is_active)  # Default value

    def test_social_media_link_str_representation(self):
        """Test the string representation of SocialMediaLink."""
        social = SocialMediaLink.objects.create(**self.valid_social_data)
        expected_str = "LinkedIn - Bean Software LinkedIn"
        self.assertEqual(str(social), expected_str)

    def test_formatted_follower_count_property(self):
        """Test the formatted_follower_count property."""
        # Test thousands
        social = SocialMediaLink.objects.create(**self.valid_social_data)
        self.assertEqual(social.formatted_follower_count, "1.5K")

        # Test millions
        social.follower_count = 2500000
        self.assertEqual(social.formatted_follower_count, "2.5M")

        # Test regular numbers
        social.follower_count = 500
        self.assertEqual(social.formatted_follower_count, "500")

        # Test None
        social.follower_count = None
        self.assertIsNone(social.formatted_follower_count)

    def test_platform_uniqueness(self):
        """Test that platform field is unique."""
        SocialMediaLink.objects.create(**self.valid_social_data)

        # Try to create another with the same platform
        duplicate_data = self.valid_social_data.copy()
        duplicate_data['display_name'] = 'Another LinkedIn'

        with self.assertRaises(Exception):  # Should raise IntegrityError
            SocialMediaLink.objects.create(**duplicate_data)
